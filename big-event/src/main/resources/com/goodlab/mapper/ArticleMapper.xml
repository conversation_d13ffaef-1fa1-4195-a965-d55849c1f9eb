<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.goodlab.mapper.ArticleMapper">
    <!-- 动态Sql -->
    <select id="list" resultType="com.goodlab.pojo.Article">
        select * from article
        <where>
            <if test="categoryId != null">
                category_id = #{categoryId}
            </if>
            <if test="state != null">
                and state = #{state}
            </if>
            <if test="userId != null">
                and create_user = #{userId}
            </if>
        </where>
    </select>
    
</mapper>